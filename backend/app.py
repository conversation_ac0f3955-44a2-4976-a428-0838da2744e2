from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import requests

from models.network import db, Network

app = Flask(__name__)
CORS(app)  # Permet les requêtes CORS depuis le frontend
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///networks.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db.init_app(app)

# Création de la base une seule fois
with app.app_context():
    db.create_all()

def fetch_and_store_networks():
    with app.app_context():
        # Crée la base de données si elle n'existe pas
        db.create_all()

        # Requête à l'API CityBikes
        response = requests.get("https://api.citybik.es/v2/networks")
        if response.status_code != 200:
            print("Erreur lors de la récupération des données")
            return

        networks = response.json().get("networks", [])

        print(f"Import de {len(networks)} réseaux...")

        for net in networks:
            name = net.get("name", "Unnamed")
            company = ", ".join(net.get('company') or [])
            location = net.get("location", {})
            city = location.get("city", "Unknown")
            country = location.get("country", "Unknown")

            # Vérifie si ce réseau existe déjà (évite les doublons)
            exists = Network.query.filter_by(name=name, city=city, country=country, company=company).first()
            if not exists:
                db.session.add(Network(name=name, city=city, country=country, company=company))

        db.session.commit()
        print("Import terminé avec succès.")

@app.route("/networks", methods=['GET'])
def get_networks():
    # Get query parameters
    city = request.args.get('city')
    name = request.args.get('name')

    # Start with base query
    query = Network.query

    # Apply filters if parameters are provided
    if city:
        query = query.filter(Network.city.ilike(f'%{city}%'))
    if name:
        query = query.filter(Network.name.ilike(f'%{name}%'))

    # Execute query and get all results
    networks = query.all()
    
    # Convert to list of dictionaries
    networks_data = [{
        'name': network.name,
        'city': network.city,
        'country': network.country,
        'company': network.company,
    } for network in networks]
    
    return jsonify(networks_data)

@app.route("/networks", methods=['POST'])
def create_network():
    # Get JSON data from request
    network_data = request.get_json()
    
    # Validate required fields
    if not network_data or not all(key in network_data for key in ['name', 'company', 'city', 'country']):
        return jsonify({'error': 'Missing required fields: name, city, country'}), 400
    
    try:
        # Create a database model instance
        network = Network(
            name=network_data["name"],
            company=network_data.get("company", ""),
            city=network_data["city"],
            country=network_data["country"],
        )

        # Add it to the session and commit
        db.session.add(network)
        db.session.commit()

        return jsonify({'message': 'Network added successfully', 'data': network_data}), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route("/stations", methods=['GET'])
def get_stations_for_country():
    # Get country parameter
    country = request.args.get('country')

    # Check if country parameter is provided
    if not country:
        return jsonify({'error': 'Country parameter is required'}), 400

    # Get all networks for the specified country
    networks_in_country = Network.query.filter_by(country=country).all()
    total_stations = len(networks_in_country)

    return jsonify({
        'country': country,
        'total_stations': total_stations,
        'networks': [{'name': n.name, 'city': n.city} for n in networks_in_country]
    })

@app.route("/stations/summary", methods=['GET'])
def get_stations_summary():
    # Get summary of stations by country
    from sqlalchemy import func

    country_stats = db.session.query(
        Network.country,
        func.count(Network.id).label('station_count')
    ).group_by(Network.country).all()

    summary = [{'country': country, 'stations': count} for country, count in country_stats]

    return jsonify(summary)

if __name__ == "__main__":
    fetch_and_store_networks()
    app.run(debug=True)
